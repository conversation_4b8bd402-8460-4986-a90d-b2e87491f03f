/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 09/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.actionbar.bars;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.contrum.prisma.utils.Cooldown;

import javax.annotation.Nullable;
import java.time.Duration;

@Getter @Setter
public abstract class ProfileActionBar extends ActionBar {


    @Nullable private Duration duration;
    @Nullable private Cooldown cooldown;
    private boolean active = false;

    private boolean canceled = false;

    public ProfileActionBar(String ID, Duration duration, int priority){
        super(ID, priority);
        this.duration = duration;
    }

    public boolean isActive(){
        return !canceled && !hasExpired();
    }

    public boolean hasExpired() {
        return active && (cooldown == null || cooldown.hasExpired());
    }

    public void start(){
        Preconditions.checkArgument(!active, "ActionBar is already started!");

        if (this.duration != null) this.cooldown = new Cooldown(duration);
        this.active = true;
    }

    public void cancel(){
        this.canceled = true;
    }
}