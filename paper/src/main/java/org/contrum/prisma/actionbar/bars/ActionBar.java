package org.contrum.prisma.actionbar.bars;

import lombok.Getter;
import org.bukkit.entity.Player;

@Getter
public abstract class ActionBar {
    private final String ID;
    private final int priority;

    public ActionBar(String ID, int priority) {
        this.ID = ID;
        this.priority = priority;
    }

    public boolean canUpdate(Player player) {
        return true;
    }

    public void apply(Player player, String text) {
        org.contrum.chorpu.xseries.messages.ActionBar.sendActionBar(player, text);
    }

    public abstract void update(Player player);
}
