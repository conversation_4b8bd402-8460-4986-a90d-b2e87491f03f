package org.contrum.prisma.actionbar;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.actionbar.bars.ActionBar;
import org.contrum.prisma.actionbar.bars.GlobalActionBar;
import org.contrum.prisma.actionbar.bars.ProfileActionBar;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.tritosa.Translator;

import java.util.ArrayList;
import java.util.List;

public class ActionBarRunnable implements Runnable {

    private final ActionBarService actionBarService;
    private final PaperServices services;
    private final Translator translator;

    public ActionBarRunnable(ActionBarService actionBarService) {
        this.actionBarService = actionBarService;
        this.services = actionBarService.getServices();
        this.translator = actionBarService.getServices().getTranslator();
    }

    @Override
    public void run() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

            //Get compatible global actionbars
            List<GlobalActionBar> activeBars = new ArrayList<>();
            for (GlobalActionBar bar : actionBarService.getGlobalActionBars()) {
                if (bar.canUpdate(player)) {
                    activeBars.add(bar);
                }
            }

            //Get highest actionbar
            ActionBar bar = actionBarService.getHighestBar(activeBars.stream().map(a -> (ActionBar) a).toList(), metadata.getHighestActionBar());

            //Remove actionbar and player metadata if isn't active
            if (bar instanceof ProfileActionBar profileActionBar && (profileActionBar.isCanceled() || profileActionBar.hasExpired())) {
                metadata.removeActionBar(bar.getID());
                profileActionBar.setActive(false);
                continue;
            }

            //Send actionbar
            if (bar == null) continue;
            bar.update(player);
        }
    }
}