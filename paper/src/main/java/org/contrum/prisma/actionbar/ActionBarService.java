/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 09/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.actionbar;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.actionbar.bars.ActionBar;
import org.contrum.prisma.actionbar.bars.GlobalActionBar;
import org.contrum.prisma.actionbar.bars.ProfileActionBar;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;

import java.util.ArrayList;
import java.util.List;

@Getter
public class ActionBarService {

    private final PaperServices services;
    private final List<GlobalActionBar> globalActionBars = new ArrayList<>();

    public ActionBarService(PaperServices services){
        this.services = services;

        Bukkit.getScheduler().runTaskTimerAsynchronously(services.getPlugin(), new ActionBarRunnable(this), 0L, 1L);
    }

    public void registerAction(ProfileActionBar bar, Player player){
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        metadata.addActionBar(player, bar);
    }

    public void removeAction(ProfileActionBar bar, Player player){
        this.removeAction(bar.getID(), services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class));
    }

    public void removeAction(String ID, Player player){
        this.removeAction(ID, services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class));
    }

    public void removeGlobalActionbar(String Id){
        for (GlobalActionBar actionbar : this.globalActionBars) {
            if (actionbar.getID().equals(Id)) {
                this.globalActionBars.remove(actionbar);
                return;
            }
        }
    }

    public void removeGlobalActionbar(GlobalActionBar bar){
        this.globalActionBars.remove(bar);
    }

    public void removeAction(String ID, ProfilePaperMetadata metadata){
        metadata.removeActionBar(ID);
    }

    public void registerGlobalActionBar(GlobalActionBar bar){
        this.globalActionBars.add(bar);
    }

    public ActionBar getHighestBar(List<ActionBar> barList, ActionBar... bars){
        ActionBar highestBar = null;
        for (ActionBar bar : barList) {
            if (bar != null && (highestBar == null || highestBar.getPriority() < bar.getPriority())) highestBar = bar;
        }
        for (ActionBar bar : bars) {
            if (bar != null && (highestBar == null || highestBar.getPriority() < bar.getPriority())) highestBar = bar;
        }
        return highestBar;
    }
}