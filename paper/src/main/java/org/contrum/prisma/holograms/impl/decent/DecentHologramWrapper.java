package org.contrum.prisma.holograms.impl.decent;

import eu.decentsoftware.holograms.api.holograms.HologramLine;
import eu.decentsoftware.holograms.api.holograms.HologramPage;
import org.bukkit.Location;
import org.contrum.prisma.holograms.Hologram;

import java.util.List;

public class DecentHologramWrapper extends Hologram {

    private final eu.decentsoftware.holograms.api.holograms.Hologram hologram;

    public DecentHologramWrapper(eu.decentsoftware.holograms.api.holograms.Hologram hologram) {
        this.hologram = hologram;
    }

    @Override
    public void destroy() {
        hologram.delete();
    }

    @Override
    public void setDisplayRange(int range) {
        hologram.setDisplayRange(range);
    }

    @Override
    public List<String> getLines(int page) {
        return hologram.getPage(page).getLines().stream().map(HologramLine::getContent).toList();
    }

    @Override
    public void setLines(int page, List<String> lines) {
        HologramPage p = hologram.getPage(page);

        for (int i = 0; i < lines.size(); i++) {
            p.setLine(i, lines.get(i));
        }
    }

    @Override
    public void setLocation(Location location) {
        hologram.setLocation(location);
    }
}
