package org.contrum.prisma.holograms.impl.fancy;

import de.oliver.fancyholograms.api.data.TextHologramData;
import org.bukkit.Location;
import org.contrum.prisma.holograms.Hologram;

import java.util.List;

public class FancyHologramWrapper extends Hologram {

    private final de.oliver.fancyholograms.api.hologram.Hologram hologram;

    public FancyHologramWrapper(de.oliver.fancyholograms.api.hologram.Hologram hologram) {
        this.hologram = hologram;
    }

    @Override
    public void destroy() {
        hologram.deleteHologram();
    }

    @Override
    public void setDisplayRange(int range) {
        hologram.getData().setVisibilityDistance(range);
        hologram.queueUpdate();
    }

    @Override
    public List<String> getLines(int page) {
        return ((TextHologramData) hologram.getData()).getText();
    }

    @Override
    public void setLines(int page, List<String> lines) {
        ((TextHologramData) hologram.getData()).setText(lines);
        hologram.queueUpdate();
    }

    @Override
    public void setLocation(Location location) {
        hologram.getData().setLocation(location);
        hologram.queueUpdate();
    }
}
