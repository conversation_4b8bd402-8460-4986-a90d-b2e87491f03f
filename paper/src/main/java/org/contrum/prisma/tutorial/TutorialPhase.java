package org.contrum.prisma.tutorial;

import lombok.Getter;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.tick.Tickable;

@Getter
public abstract class TutorialPhase implements Tickable {

    private final PaperServices services;
    private final Tutorial tutorial;

    protected TutorialPhase(PaperServices services, Tutorial tutorial) {
        this.services = services;
        this.tutorial = tutorial;
    }

    public abstract void init();

    public abstract void stop();

    private int ticks = 0;

    @Override
    public void tick() {
        ticks++;
    }
}