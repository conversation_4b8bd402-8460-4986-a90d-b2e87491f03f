package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.prisma.watch.antidupe.WatchAntiDupeService;
import org.contrum.prisma.watch.antidupe.menu.AntiDupeItemInfoMenu;
import org.contrum.prisma.watch.antidupe.menu.AntiDupeItemListMenu;

@CommandAlias("antidupe") @CommandPermission("core.command.antidupe")
public class AntiDupeCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private WatchAntiDupeService antiDupeService;

    @Subcommand("bypass")
    public void bypass(Player player) {
        if (antiDupeService.isBypass(player)) {
            player.sendMessage(CC.translate("&aTu bypass está activado."));
        } else {
            player.sendMessage(CC.translate("&cTu bypass está desactivado."));
        }
    }

    @Subcommand("bypass toggle")
    public void bypass(Player player, @Optional OnlinePlayer other) {
        Player target = other != null ? other.getPlayer() : player;

        boolean bypass = antiDupeService.isBypass(target);
        antiDupeService.setBypass(target, !bypass);

        if (bypass) {
            player.sendMessage(CC.translate("&cEl bypass de " + target.getName() + " ha sido desactivado."));
        } else {
            player.sendMessage(CC.translate("&aEl bypass de " + target.getName() + " ha sido activado."));
        }
    }

    @Subcommand("check toggle")
    public void toggle(Player player) {
        if (antiDupeService.isProcessEnabled()) {
            antiDupeService.setProcessEnabled(false);
            player.sendMessage(CC.translate("&cLa verificación de items ha sido desactivada."));
        } else {
            antiDupeService.setProcessEnabled(true);
            player.sendMessage(CC.translate("&aLa verificación de items ha sido activada."));
        }
    }

    @Subcommand("check performOnline")
    public void onlineCheck(Player player) {
        antiDupeService.performOnlinePlayersSearch();
    }

    @Subcommand("check performOffline")
    public void offlineCheck(Player player) {
        antiDupeService.performOfflinePlayerSearch();
    }

    @Subcommand("item info")
    public void itemInfo(Player player) {
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item == null || item.isEmpty()) {
            player.sendMessage("§cNo tienes ningún objeto en la mano.");
            return;
        }

        String id = antiDupeService.getItemManager().getId(item);
        if (id == null) {
            player.sendMessage("§cEl objeto no tiene un ID asignado.");
            return;
        }

        antiDupeService.getItemManager().getOrCreateWADItem(player, item).whenComplete(((wadItem, throwable) -> {
            TaskUtil.run(services.getPlugin(), () -> {
                new AntiDupeItemInfoMenu(services, wadItem, item).open(player);
            });
        }));
    }

    @Subcommand("item reset")
    public void resetItem(Player player) {
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item == null || item.isEmpty()) {
            player.sendMessage("§cNo tienes ningún objeto en la mano.");
            return;
        }

        antiDupeService.getItemManager().resetItem(item);
        player.sendMessage(CC.translate("&aEl ID del objeto ha sido reseteado."));
    }

    @Subcommand("item search")
    @Syntax("[onlyDuped = true] [global = false] [duration = global] [material = none]")
    @CommandCompletion("true|false true|false 30m @materials")
    public void search(Player player, @Optional Boolean onlyDuped, @Optional Boolean global, @Optional String duration, @Optional Material material) {
        WatchAntiDupeService.WADItemFilter filter = WatchAntiDupeService.WADItemFilter.create();

        if (onlyDuped != null) {
            filter.onlyDuped(onlyDuped);
        }

        if (global != null) {
            filter.onlyCurrentServer(!global);
        }

        if (duration != null) {
            filter.minDate(System.currentTimeMillis() - TimeUtils.parseDuration(duration).toMillis());
        }

        if (material != null && material != Material.AIR) {
            filter.material(material);
        }

        player.sendMessage(CC.translate("&aBuscando items..."));
        antiDupeService.findItems(filter).whenComplete(((wadItems, throwable) -> {
            if (throwable != null) {
                throwable.printStackTrace();
                return;
            }

            new AntiDupeItemListMenu(services, wadItems, filter.isOnlyDuped(), filter.isOnlyDuped()).openAsync(player, services.getPlugin());
        }));
    }
}
